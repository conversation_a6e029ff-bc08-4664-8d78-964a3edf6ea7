import duplicates as dup
import sys
import os
import duplicate
import shutil
import random

# Importing Libraries 
import os 
from pathlib import Path 
from filecmp import cmp

def get_all_files(directory):
    all_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            all_files.append(Path(root) / file)
    return all_files

fils = os.listdir(".")
random.shuffle(fils)
for folder in fils:
    if os.path.isdir(folder) and folder not in ["_duplicates", "zero_byte_files", "_csv"]:
        print(folder)
        DATA_DIR = Path(folder)
        files = sorted(get_all_files(DATA_DIR))

        # List having the classes of documents 
        # with the same content 
        duplicateFiles = []

        zero_byte_files = []

        # comparison of the documents 
        for file_x in files:
            if os.path.isdir(file_x):
                print(f"Skipping {file_x}")
                continue
            if os.path.getsize(file_x) < 150:
                zero_byte_files.append(file_x)
                continue
            if_dupl = False

        #     for class_ in duplicateFiles: 
        #         # Comparing files having same content using cmp() 
        #         # class_[0] represents a class having same content 
        #         if_dupl = cmp( 
        #             file_x, 
        #             DATA_DIR / class_[0], 
        #             shallow=False
        #         ) 
        #         if if_dupl: 
        #             class_.append(file_x) 
        #             break

        #     if not if_dupl: 
        #         duplicateFiles.append([file_x]) 

        # # Print results 
        # for i in duplicateFiles:
        #     i_ = 0
        #     if len(i) > 1:
        #         for j in i:
        #             i_ += 1
        #             print(f"{i_} {j}")
        #             try:
        #                 shutil.move(f"{folder}\\{j}", f"_duplicates")
        #             except:
        #                 pass
        for z in zero_byte_files:
            try:
                shutil.move(file_x, f"zero_byte_files/{random.randint(0, 100000)}_{z.name}")
            except:
                pass




  
  
# list of all documents 
