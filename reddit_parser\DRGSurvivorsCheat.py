from ReadWriteMemory import ReadWriteMemory
from pymem import *
from pymem.process import *
rwm = ReadWriteMemory()

EXE = "DRG Survivor.exe"

process = rwm.get_process_by_name(EXE)
if process:
    process.open()

GAssemblyDLLPointer = 0x7FFF519A
baseAddress = 0x7FF69027+GAssemblyDLLPointer


pm = pymem.Pymem(EXE)


coins = process.get_pointer(baseAddress, [0x40, 0x140, 0x150, 0xd0, 0x8A8, 0x20, 0x54])
pass