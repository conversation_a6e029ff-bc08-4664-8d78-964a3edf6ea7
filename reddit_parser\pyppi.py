import praw
import requests
import os
import json
import time

def save_to_json(text, subreddit):
    # increment json file with text
    with open(f"subreddit_data\\{subreddit}.json", "r") as file:
        data = json.load(file)
        data.append(text)
    with open(f"subreddit_data\\{subreddit}.json", "w") as file:
        json.dump(data, file)

def in_json(text, subreddit):
    # check if text is in json file
    if not os.path.exists(f"subreddit_data\\{subreddit}.json"):
        with open(f"subreddit_data\\{subreddit}.json", "w") as file:
            json.dump([], file)
    with open(f"subreddit_data\\{subreddit}.json", "r") as file:
        data = json.load(file)
        if text in data:
            return True
    return False


reddit = praw.Reddit(
    client_id="1xuOe4TdW2aOwpwOGtmJpg", 
    client_secret="n_yPpfdbFJiuFnZS8qDQuK53hkuo7A",
    password="2%VYpL9s3YcQ@3CZ%22C5NM6GxZeiEVqHYR^MHKaCVDmeqdVHWAab",
    user_agent="testscript by u/PeepoPos",
    username="PeepoPos",
)

def get_all_files_recursively_only_return_filenames(directory):
    files = []
    for path, _, filenames in os.walk(directory):
        for filename in filenames:
            filename = filename.split("\\")[-1]
            files.append(filename)
    return files
subreddits2 = ["BBC_SissyCaptions", "BimboSissyDolls","bitchsissies","sissy_caption","SissyGifs","SissyCaptionStories","SissyHeaven","ForcedFeminization","sissyology","Sissy_Captions","Sissy_Porn","GOONED","BlackOwnedSissies",
              "sissycaptions","sissyhypno","SissyFemdom","SissyHentai","SissyChastity","sissyhentai2","sissyhentaicomics","sissy_harem","SissyHub__","Sissy_Positive",
            "LimpClittySissy","sissy_bbc_dreams","SissySchoolgirls",]
subreddits1 = ["MasturbationHentai","yuri","YuriHentai",  "FemPOVHentai","IWantWLWHentai", "HentaiForHer","SubmissiveMaleHentai", "IWantToBeHerHentai", "iwanttobeher","hentai","hentaicaptions","hentai_sex_videos_hub","JavSeduction", "PetiteJAV","JavHentai_Official","JavPornDaily","PetPlayBDSM","PetiteGoneWild","petite","petplay","SuccubusPics","succubus","HypnoHentai","HypnoGoneWild","HypnoBoobies","HypnoHookup","HypnoCaps","hypnosis","HypnoFur","HypnoFair","Hypnolovers","BBC_SissyCaptions","InterracialCaptions", "InterracialBbcSluts","InterracialHardcore","Interracial_Erotica","BBCaddicts", "BBClust","bbc","HorsecockCreampie","knotfurryporn","HorseCum","HorsecockFutanariLove","HorsecockHaven","FemboyFutaCoalition","FurryFutaSecondWind","HorsecocksMasterRace","futanari","futanari_Comics","FutaCum","FutanariHentai","FutaTrap","Futadomworld","FutanariGifs","FutaAI","Futaonfemale","furryporn","Furry_incest","HorsecockFuta","HENTAI_GIF","HentaiBeast","hentaibondage","HentaiAndRoleplayy","Hentai__videos","Hentai_Interracial", "Drawn_horsecock", "HentaiAnal", "iwanttobeherhentai2", "SissyHentai", "SissyHentaiCaptions", "SissyHentaiBDSM", "SissyHentai2", "Femboyhentai", "Sissylink","futacaptions", "hentaichastity"]

    
subreddits3 = ["teenagers", "GOONED","traphentai","PublicHentai","CumHentai","MagicHentai","NunHentai","Hentai_Interracial","TransHentai","HentaiPetgirls","OralHentai","MaidHentai","EmbarrassedHentai","HentaiCumsluts","ElfHentai","FlatChestHentai",
               "JerkOffToAnime","FreeuseHentai","rape_hentai","FaceFuckHentai","AssJobHentai","Total_Hentai","CuddlesAndHentai","AnalHentai","NuxTakuSubmissions","FateHentai","BimboHentai","Uniform_Hentai","HentaiVTuberGirls",
               "BlackedHentaiandRP", "HentaiBlackedCaptions", "BLACKEDPAWGS", "HentaiBreeding", "BBCparadise", "BlackWorldOrder", "PawgRiding", "WhiteBoySex", "BNWO_Captions", "betatraining",
               "cuckhumiliation","Sissykik2","BBCCheatingCuckolding","BNWOFantasy","whiteboydiscussion","CocksCumAndCuckold","my_sissycaptions","SissyCaptionsHypno",
               "jerkbudsHentai","MhaRPandHentai","HentaiBNWO_Revived",]

subreddits = subreddits1 + subreddits2 + subreddits3

import random
#random.shuffle(subreddits)

huh = []

real = reddit.subreddits.search_by_name("sissy", exact=False)
r2 =reddit.subreddits.search_by_name("caption", exact=False)
r3 = reddit.subreddits.search("blacked", limit=1000)
#r3= reddit.subreddits.search_by_name("furry", exact=False)
#r4 = reddit.subreddits.search_by_name("femboy", exact=False)
v = ""
# for i in real:
#     v = f"{v}\"{i.display_name}\"," if i.display_name not in subreddits else v
# for i in r2:
#     v = f"{v}\"{i.display_name}\"," if i.display_name not in subreddits else v

sorted_subreddits = []
for i in r3:
    if not i.subscribers:
        continue
    else:
        sorted_subreddits.append(i)
sorted_subreddits = sorted(sorted_subreddits, key=lambda x: x.subscribers, reverse=True)
for i in sorted_subreddits:
    if i.subscribers and i.subreddit_type != 'private' and i.subscribers > 5000:
        v = f"{v}\"{i.display_name}\"," if i.display_name not in subreddits else v
print(v)    
    
def download_media(submission, sub, files, retry=False):
    filename = submission.url.split("/")[-1]
    if "?" in filename:
        filename = filename.split("?")[0]
    if not os.path.exists(sub):
        os.makedirs(sub)
    
    if os.path.exists(f"{sub}\\{filename}") or in_json(submission.url, sub):
        #print(f"Already downloaded {filename}")
        return
    try:
        req = requests.get(submission.url).content
    except Exception as e:
        print(e)
        time.sleep(1)
        if not retry:
            download_media(submission, sub, files, retry=True)
        else:
            return
    try:
        if len(req) < 190:
            return
    except:
        return
    with open(f"{sub}\\{filename}", "wb") as f:
        try:
            

            
            # if size is less than 1mb
            
            f.write(req)
            wtf = int(os.path.getsize(f"{sub}\\{filename}"))
            print(wtf)
            if wtf < 190:
                os.remove(f"{sub}\\{filename}")
                return
            save_to_json(submission.url, sub)

        except Exception as e:
            print(e)
            return
    #print(f"Downloaded {filename}")

files = get_all_files_recursively_only_return_filenames(".")
# if folder exists skip

random.shuffle(subreddits)

for sub in subreddits:
    
    # if os.path.exists(sub):
    #     continue
    print(f"Downloading from {sub}")

    subreddit = reddit.subreddit(sub)
    submissions = []
    
    
    for submission in subreddit.top(limit=1000):
        
        if submission not in submissions:
            filename = submission.url.split("/")[-1]
            if "?" in filename:
                filename = filename.split("?")[0]
            if filename in files:
                #Already downloaded {filename}")
                continue
            submissions.append(submission)
            if ".gif" in submission.url or ".jpg" in submission.url or ".png" in submission.url or ".mp4" in submission.url:
                download_media(submission, sub, files)

    for submission in subreddit.hot(limit=1000):
        if submission not in submissions:
            filename = submission.url.split("/")[-1]
            if "?" in filename:
                filename = filename.split("?")[0]
            if filename in files:
                #Already downloaded {filename}")
                continue
            submissions.append(submission)
            if ".gif" in submission.url or ".jpg" in submission.url or ".png" in submission.url or ".mp4" in submission.url:
                download_media(submission, sub, files)

    for submission in subreddit.new(limit=1000):
        if submission not in submissions:
            filename = submission.url.split("/")[-1]
            if "?" in filename:
                filename = filename.split("?")[0]
            if filename in files:
                #Already downloaded {filename}")
                continue
            submissions.append(submission)
            if ".gif" in submission.url or ".jpg" in submission.url or ".png" in submission.url or ".mp4" in submission.url:
                download_media(submission, sub, files)

    for submission in subreddit.controversial(limit=1000):
        if submission not in submissions:
            filename = submission.url.split("/")[-1]
            if "?" in filename:
                filename = filename.split("?")[0]
            if filename in files:
                #Already downloaded {filename}")
                continue
            submissions.append(submission)
            if ".gif" in submission.url or ".jpg" in submission.url or ".png" in submission.url or ".mp4" in submission.url:
                download_media(submission, sub, files)

    for submission in subreddit.rising(limit=1000):
        if submission not in submissions:
            filename = submission.url.split("/")[-1]
            if "?" in filename:
                filename = filename.split("?")[0]
            if filename in files:
                #Already downloaded {filename}")
                continue
            submissions.append(submission)
            if ".gif" in submission.url or ".jpg" in submission.url or ".png" in submission.url or ".mp4" in submission.url:
                download_media(submission, sub, files)

    print(f"Total submissions: {len(submissions)}")

    try:
        if len(os.listdir(sub)) < 1:
            os.rmdir(sub)
    except:
        pass